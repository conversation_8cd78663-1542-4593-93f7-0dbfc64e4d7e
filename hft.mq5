//+------------------------------------------------------------------+
//|                  MomentumTrailingStop_MT5.mq5 v1.21              |
//+------------------------------------------------------------------+
#property copyright "© 2025 OpenAI"
#property version   "1.21"
#property strict

#include <Trade\Trade.mqh>
CTrade trade;

//──────────────────────────── Inputs ────────────────────────────//
input double         ImpulseThresholdPerc = 1.0;    // % change between last 2 closed bars
input bool           UseATRTrail          = true;   // true → ATR × multiplier; false → fixed %
input double         ATRMultiplier        = 1.0;    // if UseATRTrail
input double         TrailStopPerc        = 0.5;    // if NOT UseATRTrail (as % of price)
input int            ATRPeriod            = 14;     // ATR look-back length
input ENUM_TIMEFRAMES ATR_TF               = PERIOD_CURRENT; // timeframe for ATR

input double         Lots                 = 0.10;   // order volume (lots)
input double         SpreadBufferPips     = 0.3;    // extra pips added to entries & stops
input int            SlippagePoints       = 5;      // allowed slippage on modify

//──────────────────────────── Globals ────────────────────────────//
int      atr_handle    = INVALID_HANDLE;  // ATR indicator handle
datetime last_bar_ts   = 0;               // to detect new bar
double   pip_points    = 0.0;             // 1 "pip" in points

//───────────────────────── Helper funcs ──────────────────────────//
// Return 1 "pip" in broker points
double PipPoints()
{
   int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);
   return (digits == 3 || digits == 5) ? _Point * 10 : _Point;
}
// Extra safety margin in points
double BufferPts() { return SpreadBufferPips * pip_points; }
// True once per newly closed bar
bool NewBar()
{
   datetime t0 = iTime(_Symbol, _Period, 0);
   if(t0 != last_bar_ts)
   {
      last_bar_ts = t0;
      return true;
   }
   return false;
}
// Get latest ATR (closed bar)
double GetATR()
{
   double buf[];
   if(CopyBuffer(atr_handle, 0, 1, 1, buf) == 1)
      return buf[0];
   return 0.0;
}
// Check for existing LongSTOP/ShortSTOP pending orders
bool PendingExists()
{
   // iterate all current open and pending orders
   for(int i = 0; i < OrdersTotal(); i++)
   {
      if(!OrderSelect(i, SELECT_BY_POS, SELECT_TYPE_TRADES))
         continue;
      ENUM_ORDER_TYPE ot = (ENUM_ORDER_TYPE)OrderGetInteger(ORDER_TYPE);
      string cmt = OrderGetString(ORDER_COMMENT);
      if((ot == ORDER_TYPE_BUY_STOP && cmt == "LongSTOP") ||
         (ot == ORDER_TYPE_SELL_STOP && cmt == "ShortSTOP"))
         return true;
   }
   return false;
}

//──────────────────────── Entry logic ───────────────────────────//
void CheckImpulse()
{
   double c1 = iClose(_Symbol, _Period, 1);
   double c2 = iClose(_Symbol, _Period, 2);
   if(c2 == 0) return;

   double change = 100.0 * ((c1 / c2) - 1.0);
   if(PendingExists()) return; // avoid duplicates

   double buffer = BufferPts();
   double high1  = iHigh(_Symbol, _Period, 1);
   double low1   = iLow(_Symbol, _Period, 1);

   trade.SetDeviationInPoints(SlippagePoints);

   if(change > ImpulseThresholdPerc)
   {
      double price = high1 + buffer;
      trade.BuyStop(Lots, price, _Symbol, 0.0, 0.0, ORDER_TIME_GTC, 0, "LongSTOP");
   }
   else if(change < -ImpulseThresholdPerc)
   {
      double price = low1 - buffer;
      trade.SellStop(Lots, price, _Symbol, 0.0, 0.0, ORDER_TIME_GTC, 0, "ShortSTOP");
   }
}

//──────────────────── Trailing-stop logic ───────────────────────//
void TrailPositions()
{
   double atr    = GetATR();
   double buffer = BufferPts();
   int    stops  = (int)SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL);

   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(!PositionSelectByIndex(i))
         continue;
      ulong ticket = PositionGetInteger(POSITION_TICKET);
      ENUM_POSITION_TYPE type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
      double cur_sl = PositionGetDouble(POSITION_SL);
      double ref_prc = (type == POSITION_TYPE_BUY)
                        ? SymbolInfoDouble(_Symbol, SYMBOL_BID)
                        : SymbolInfoDouble(_Symbol, SYMBOL_ASK);

      double dist = UseATRTrail ? atr * ATRMultiplier : ref_prc * TrailStopPerc / 100.0;
      dist = MathMax(dist, stops * _Point + buffer);

      double new_sl = (type == POSITION_TYPE_BUY) ? ref_prc - dist : ref_prc + dist;
      bool tighten = (type == POSITION_TYPE_BUY && (cur_sl == 0.0 || new_sl > cur_sl + _Point)) ||
                     (type == POSITION_TYPE_SELL && (cur_sl == 0.0 || new_sl < cur_sl - _Point));

      if(tighten)
      {
         trade.SetDeviationInPoints(SlippagePoints);
         trade.PositionModify(ticket, new_sl, 0.0);
      }
   }
}

//────────────────────────── EA events ───────────────────────────//
int OnInit()
{
   pip_points = PipPoints();
   atr_handle = iATR(_Symbol, ATR_TF, ATRPeriod);
   if(atr_handle == INVALID_HANDLE)
      return INIT_FAILED;
   return INIT_SUCCEEDED;
}

void OnDeinit(const int reason)
{
   if(atr_handle != INVALID_HANDLE)
      IndicatorRelease(atr_handle);
}

void OnTick()
{
   if(NewBar())
      CheckImpulse();
   TrailPositions();
}
//+------------------------------------------------------------------+
